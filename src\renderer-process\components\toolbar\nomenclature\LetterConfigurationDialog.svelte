<script>
    import { _isConfiguringLetter } from '@store';
    import { alfabeti, formatAlphabet } from '@utility';
    import { createEventDispatcher } from 'svelte';

    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';
    import Textfield from '@smui/textfield';
    import Radio from '@smui/radio';
    import FormField from '@smui/form-field';

    let letterStart = 'A';
    let letterEnd = 'Z';
    let numericProgression = 1;
    let alphabet = alfabeti[0];
    const dispatch = createEventDispatcher();

    const handleConfirm = () => { 
        const progressioniList = [];

        /* Create letter list from start and end letters and selected alphabet */
        const formattedAlphabet = formatAlphabet(alphabet, letterStart);

        let letterStartIndex = formattedAlphabet.letters.findIndex((el) => el == letterStart);
        let letterEndIndex = letterEnd ? formattedAlphabet.letters.findIndex((el) => el == letterEnd) : letterStartIndex;

        let curLetterIndex = letterStartIndex;
        let curNumber = numericProgression ? 1 : null;
        
        while(curLetterIndex <= letterEndIndex) {
            progressioniList.push({letter: formattedAlphabet.letters[curLetterIndex], number: curNumber});

            if(!curNumber)
                curLetterIndex ++;
            else if(curNumber + 1 <= numericProgression) 
                curNumber++;
            else {
                curNumber = 1;
                curLetterIndex ++;
            }
        }

        console.log("progressioniList: ", progressioniList);

        /* Build progressione string */
        const progressione = `${letterStart};${letterEnd};${numericProgression};${alphabet.name}`;  

        dispatch('confirm', {progressioniList: progressioniList, progressione: progressione});
        _isConfiguringLetter.set(false);
    }
</script>

<div>
    <Dialog bind:open={$_isConfiguringLetter} scrimClickAction="">
        <Title>Configura logica di progressione</Title>
        <Content>
            <div class="flex-column">
                
                <!-- PROGRESSIONE -->
                <div class="flex-row" style="margin-bottom: 30px;">
                    <Textfield
                        label="Lettera Inizio"
                        variant="outlined"
                        type="text"
                        bind:value={letterStart}
                    />
                    <span>-</span>
                    <Textfield
                        label="Lettera Fine"
                        variant="outlined"
                        type="text"
                        bind:value={letterEnd}
                    />
                    <Textfield
                        label="Progressione"
                        variant="outlined"
                        type="number"
                        input$min="1"
                        input$emptyValueUndefined
                        bind:value={numericProgression}
                    />
                </div>

                <!-- ALFABETO -->
                <div style="display: flex;">
                    <p style="color: var(--mdc-theme-primary);">Alfabeto:</p>
                    <div class="flex-row-radio" >
                        {#each alfabeti as curAlphabet}
                            <FormField>
                                <Radio bind:group={alphabet} value={curAlphabet}/>
                                <span class="radio-label" slot="label">{curAlphabet.name}</span>
                            </FormField>
                        {/each}
                    </div>
                </div>
            </div>
        </Content>
        <div class="flex-row-buttons">
            <Button on:click={handleConfirm} variant="raised" disabled={!letterStart}>
                <Label>Conferma</Label>
            </Button>
        </div>
    </Dialog>
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .flex-row {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }

    .flex-row-radio {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 80%;
    }

    .flex-row-buttons {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    p, span {
        color: var(--mdc-theme-secondary);
        margin: 0px;
        padding-top: 8px;
    }

    /* Radio */
    * :global(.mdc-radio) {
        padding-right: 0;
    }

    * :global(.mdc-form-field) {
        color: var(--mdc-theme-secondary);
    }

    /* Dialog */
    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 25%;
        min-height: 25%;
    }

    * :global(.mdc-dialog__content) {
        padding-top: 20px;
    }

    /* Textfield */
    * :global(.mdc-text-field) {
        height: 40px;
        width: 120px;
    }
</style>