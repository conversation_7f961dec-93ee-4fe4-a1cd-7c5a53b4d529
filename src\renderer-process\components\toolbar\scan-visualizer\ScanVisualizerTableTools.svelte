<script>
    import { _selectedJob, _selectedScan, _isDoublePageEnabled, _isReplaceEnabled, _isInsertEnabled, _isEditingScan, _mainViewer, _mainViewerBounds, _insertReminderId, _replaceReminderId, _user, _scannerId, _isSettingsFormOpen, _isSettingColors, _flipScan, _rotationDegree } from "@store";
    import ConfirmationDialog from "@components/common/ConfirmationDialog.svelte";
    import { notify, reminder, removeAllSelectedClass } from '@utility';
    import { replace } from 'svelte-spa-router';
    import * as d3 from 'd3-selection';
    import { onMount } from "svelte";

    //smui
    import SegmentedButton, { Segment } from '@smui/segmented-button';
   
    //icons
    import StandardIcon from "svelte-material-icons/Camera.svelte";
    import AddIcon from "svelte-material-icons/CameraPlus.svelte";
    import ReplaceIcon from "svelte-material-icons/CameraRetake.svelte";
    import RemoveIcon from "svelte-material-icons/ImageRemove.svelte";
    import EditIcon from "svelte-material-icons/ImageEdit.svelte";
    import PreviewIcon from "svelte-material-icons/LinkVariant.svelte";

    let openConfirmationDialog;
    let confirmationDialogAction;

    onMount(() => {
        /* Initialiaze acquisition mode to standard*/
        initAcquisitionMode();

        /* Create event listeners */
        createEventListeners();
    })

    const initAcquisitionMode = () => {
        /* Clear insert mode */
        if($_isInsertEnabled) _isInsertEnabled.set(false);
        if($_insertReminderId) reminder.remove($_insertReminderId);

        /* Clear replace mode */
        if($_isReplaceEnabled) _isReplaceEnabled.set(false);
        if($_replaceReminderId) reminder.remove($_replaceReminderId);
    }

    const createEventListeners = () => {
        d3.select('#scan-visualizer-table-tools').on('unselect-tool', (e) => {
            selectedTools = selectedTools.filter(el => el.name !== e.detail.name);
        });
    }

    const handleAnswer = async (e) => {
        const answer = e.detail;

        /* remove selected tools */
        selectedTools = selectedTools.filter(el => el.name !== 'Cancella');

        if(!answer) return;

        if(confirmationDialogAction == 'CANCELLA SCANSIONE')
            await doRemove();
    }

    const AcquisitionTypeSegments = [
        {
            name: 'Standard (nuova scansione in fondo)',
            icon: StandardIcon,
            clickHandler: () => {
                /* Remove insert mode if enabled */
                if($_isInsertEnabled){
                    _isInsertEnabled.set(false);
                    reminder.remove($_insertReminderId);
                    _insertReminderId.set(null);
                }

                /* Remove replace mode if enabled */
                if($_isReplaceEnabled){
                    _isReplaceEnabled.set(false);
                    reminder.remove($_replaceReminderId);
                    _replaceReminderId.set(null);
                }
            }
        },
        {
            name: 'Aggiunta (nuova scansione sopra quella selezionata)',
            icon: AddIcon,
            clickHandler: async () => {
                /* Remove replace mode if enabled */
                if($_isReplaceEnabled){
                    _isReplaceEnabled.set(false);
                    reminder.remove($_replaceReminderId);
                    _replaceReminderId.set(null);
                }

                _isInsertEnabled.set(true);
        
                /* Show reminder toast */
                if($_insertReminderId) reminder.remove($_insertReminderId);
                _insertReminderId.set(reminder.create("<strong>Modalità inserimento attiva</strong><br>La scansione verrà inserita sopra a quella selezionata"));

                /* Create event listener to remove the reminder once the scan is completed */
                d3.select('#scan-visualizer-table-tools').on('scan-insert-completed', (e) => {
                    _isInsertEnabled.set(false);
                    reminder.remove($_insertReminderId);
                    selectedAcquisitionType = AcquisitionTypeSegments[0];
                }, {once: true});
            }
        },
        {
            name: 'Sostituzione (nuova scansione al posto di quella selezionata)',
            icon: ReplaceIcon,
            clickHandler: async () => {
                /* Remove insert mode if enabled */
                if($_isInsertEnabled){
                    _isInsertEnabled.set(false);
                    reminder.remove($_insertReminderId);
                    _insertReminderId.set(null);
                }

                _isReplaceEnabled.set(true);
        
                /* Show reminder toast */
                if($_replaceReminderId) reminder.remove($_replaceReminderId);
                _replaceReminderId.set(reminder.create("<strong>Modalità sostituzione attiva</strong><br>La scansione sostituirà quella selezionata"));

                /* Create event listener to remove the reminder once the scan is completed */
                d3.select('#scan-visualizer-table-tools').on('scan-replace-completed', (e) => {
                    _isReplaceEnabled.set(false);
                    reminder.remove($_replaceReminderId);
                    selectedAcquisitionType = AcquisitionTypeSegments[0];
                }, {once: true});
            }
        }
    ];
    let selectedAcquisitionType = AcquisitionTypeSegments[0];

    let selectedTools = [];
    const toolsSegments = [
        {
            name: 'Cancella',
            icon: RemoveIcon,
            clickHandler: () => {
                if(!$_selectedScan) return notify.warning("Nessuna scansione selezionata!");

                confirmationDialogAction = 'CANCELLA SCANSIONE';
                openConfirmationDialog = true;
            }
        },
        {
            name: 'Rinomina',
            icon: EditIcon,
            clickHandler: () => {
                if(!$_selectedScan) return notify.warning("Nessuna scansione selezionata!");
                /* if($_selectedScan.progressione_sx || $_selectedScan.progressione_dx) return notify.warning("Non è possibile rinominare una scansione con progressione"); */
                _isEditingScan.set(true);
            }
        },
        {
            name: 'Visualizzazione anteprime',
            icon: PreviewIcon,
            clickHandler: () => {
                /* Reset acquisition mode to standard*/
                initAcquisitionMode();

                _mainViewerBounds.set($_mainViewer.viewport.getBounds());
                d3.select('#scan-viewers').dispatch('destroy-viewers');
                replace('/previewVisualizer');
            }
        }
    ]

    const doRemove = async () => {
        let params = {};
        params.idDigit = $_selectedJob.id_digit;
        params.sequenzialeScansione = $_selectedScan.sequenziale_scansione;
        params.step = 1;

        await window.electron.database("db-delete-scansione", { id: $_selectedScan.id });
        await window.electron.database("db-decrease-seq-scansioni", params);

        /* Cancella file tif */
        let imageToDeletePath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.scan_path, $_selectedScan.id.toString() ]);
        await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_0.tif');
        await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_1.tif');

        /* Cancella preview dzi e cartella _files */
        imageToDeletePath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, $_selectedScan.id.toString() ]);
        await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_0.dzi');
        await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_0_files');
        await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_1.dzi');
        await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_1_files');

        /* Log Event */
        let eventText = '';
        eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
        eventText += `Evento: Cancellazione Immagine\n`;
        eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
        eventText += `ID : ${$_scannerId}\n`;
        eventText += `Indice acquisizione cancellato # ${$_selectedScan.sequenziale_scansione}\n`;
        eventText += $_isDoublePageEnabled ? `${$_selectedScan.display_name_sx}, ${$_selectedScan.display_name_dx}\n\n` : `${$_selectedScan.display_name_sx}\n\n`;
        await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Cancellazione Immagine'});

        d3.select('#scan-visualizer-table').dispatch('refresh-scan-table', {detail: {updateNomenclature: true}});
        _selectedScan.set(null);
        removeAllSelectedClass();
    }
</script>

<ConfirmationDialog bind:open={openConfirmationDialog} action={confirmationDialogAction} on:answer={(e) => handleAnswer(e)}/>

<div id="scan-visualizer-table-tools" class="flex-row-container">
    <!-- Acquisition Type -->
    <SegmentedButton segments={AcquisitionTypeSegments} let:segment singleSelect bind:selected={selectedAcquisitionType} key={(segment) => segment.name}>
        <Segment {segment} title={segment.name} on:click={() => segment.clickHandler()}>
            <svelte:component this={segment.icon} width=25 height=25 viewBox='0 0 24 24' color="var(--mdc-theme-secondary)"/>
        </Segment>
    </SegmentedButton>

    <!-- Tools -->
    <SegmentedButton segments={toolsSegments} let:segment bind:selected={selectedTools} key={(segment) => segment.name}>
        <Segment {segment} title={segment.name} on:click={() => segment.clickHandler()}>
            <svelte:component this={segment.icon} width=25 height=25 viewBox='0 0 24 24' color="var(--mdc-theme-secondary)"/>
        </Segment>
    </SegmentedButton>
</div>

<style>
    .flex-row-container {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
</style>